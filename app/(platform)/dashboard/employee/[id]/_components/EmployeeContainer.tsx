"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { EmployeeNavbar } from "./EmployeeNavbar";
import { EmployeeChatSection } from "./EmployeeChatSection";
import { EmployeeWorkflowSection } from "./EmployeeWorkflowSection";
import { useEmployeeStore } from "@/hooks/use-employee";
import { chatNavbarTabs } from "@/shared/constants";
import { useQuery } from "@tanstack/react-query";
import { agentApi } from "@/app/api/agent";
import { Skeleton } from "@/components/ui/skeleton";
import { EmployeeConversationHistorySection } from "./EmployeeConversationHistorySection";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { dashboardRoute } from "@/shared/routes";

export const EmployeeContainer = () => {
  const [currentTab, setCurrentTab] = useState<
    (typeof chatNavbarTabs)[number]["value"]
  >(chatNavbarTabs[0].value); // State to store the current tab, can be Chat, Workflow or File , by default first one is Chat
  const params = useParams();
  const { setCurrentEmployeeId, currentEmployeeId } = useEmployeeStore();
  const router = useRouter();

  useEffect(() => {
    // To set the current employee id from the url parameters into the global state
    const employeeId = params.id;
    if (typeof employeeId === "string") {
      setCurrentEmployeeId(employeeId);
    } else {
      setCurrentEmployeeId(null);
      console.error("Employee ID not found or invalid in URL parameters.");
    }
  }, [params.id, setCurrentEmployeeId]);

  // Fetch agent details using React Query
  const { data: agentDetails, isLoading } = useQuery({
    queryKey: ["agent", currentEmployeeId],
    queryFn: () =>
      currentEmployeeId ? agentApi.getAgentDetails(currentEmployeeId) : null,
    enabled: !!currentEmployeeId, // Only run the query if we have an employee ID
  });

  const handleTabChange = (value: string) => {
    setCurrentTab(value);
  };

  // Get agent data directly
  const agent = agentDetails?.agent;

  // Default values if data is not available
  const defaultAvatar = "/assets/avatars/default-avatar.svg";

  // Show loading state while fetching data
  if (isLoading) {
    return (
      <div className="flex flex-col h-full items-center justify-center">
        <div className="flex flex-col gap-4 items-center">
          <Skeleton className="h-12 w-48" />
          <Skeleton className="h-24 w-24 rounded-full" />
          <Skeleton className="h-8 w-64" />
        </div>
      </div>
    );
  }

  // Render only when agent data is available
  if (!agent) {
    // Optional: Show a specific message or different loading state
    return (
      <div className="flex flex-col gap-4 h-full items-center justify-center">
        <h1 className="text-2xl font-bold font-primary text-brand-primary-font">
          Oops, Agent not found
        </h1>
        <PrimaryButton
          className="w-fit"
          onClick={() => router.push(dashboardRoute)}
        >
          Redirect Back To Dashboard
        </PrimaryButton>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <EmployeeNavbar agent={agent} onTabChange={handleTabChange} />
      {currentTab === "Chat" && <EmployeeChatSection agent={agent} />}
      {currentTab === "Workflow" && <EmployeeWorkflowSection />}
      {currentTab === "Conversation History" && (
        <EmployeeConversationHistorySection />
      )}
    </div>
  );
};
